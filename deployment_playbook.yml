---
- name: <PERSON><PERSON> and move jar files and create symlink
  hosts: all
  gather_facts: false

  tasks:

    - name: assert limit
      run_once: yes
      assert:
        that:
        - 'ansible_limit is defined'
        - 'ansible_limit != ""'
        fail_msg: Playbook must be run with a limit to an inventory
        quiet: yes


    - name: Ensure directory structure exists
      file:
        path: /sas/ma/env/java/KafkaAdapter/
        state: directory
        owner: sas
        group: sas
        mode: 0755
        recurse: yes
    - name: Find all .jar files
      find:
        paths: build/libs/
        patterns: "*.jar"
      delegate_to: localhost
      register: jar_files_result_local

    - name: Copy jar files to remote host
      copy:
        src: "{{ item.path }}"
        dest: /sas/ma/env/java/KafkaAdapter/
        owner: sas
        group: sas
        mode: 0755
      loop: "{{ jar_files_result_local.files }}"

    - name: Find the newest .jar file_change
      find:
        paths: /sas/ma/env/java/KafkaAdapter/
        patterns: integration-layer*.jar
        file_type: file
      register: jar_files_result_remote

    - name: Get latest file_change
      set_fact:
        latest_file: "{{ jar_files_result_remote.files | sort(attribute='mtime',reverse=true) | first }}"
    - name: Debug latest_file_changed
      debug:
        var: latest_file  
    - name: Create symlink to the newest jar file
      file:
        src: "{{ latest_file.path }}"
        dest: "/sas/ma/env/java/KafkaAdapter/integration-layer-SNAPSHOT.jar"
        state: link

