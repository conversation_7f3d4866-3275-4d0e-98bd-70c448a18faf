package com.sas.hccn.rtdm

import com.sas.rtdm.implementation.engine.EventInfo
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.RTDMTable.Column
import org.apache.log4j.Logger

import javax.sql.DataSource
import java.sql.*

class MyActivity implements Runnable {

    Map<String, DataSource> mapJDBC;

    //input
    RTDMTable inputDatagrid;
    String dbTable;
    String gridColumns;
    String dbColumns;
    Long logId
    String logIdColumnName

    //output:
    String status
    String statusDescription = ""
    String errorMessage;

    // Event info
    EventInfo evtInfo;
    private boolean useDB = true
    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    @Override
    void run() {
        if (dbTable == null || dbTable.isEmpty()) {
            log.error("SP_WriteDataGrid2DB - DB Table is null or empty")
            status = "ERROR"
            errorMessage = "DB Table is null or empty"
            return
        }
        if (gridColumns == null || gridColumns.isEmpty()) {
            log.error("SP_WriteDataGrid2DB - Grid columns is null or empty")
            status = "ERROR"
            errorMessage = "Grid columns is null or empty"
            return
        }
        if (dbColumns == null || dbColumns.isEmpty()) {
            log.error("SP_WriteDataGrid2DB - DB columns is null or empty")
            status = "ERROR"
            errorMessage = "DB columns is null or empty"
            return
        }

        List<String> gridColumnsList = gridColumns.split(",").collect { it.trim() }
        List<String> dbColumnsList = dbColumns.split(",").collect { it.trim() }
        if (gridColumnsList.size() != dbColumnsList.size()) {
            log.error("SP_WriteDataGrid2DB: Number of Grid columns must be equal to number of DB columns. " +
                    "Grid columns = ${gridColumnsList.size()}, DB columns = ${dbColumnsList.size()}")
            status = "ERROR"
            errorMessage = "Number of Grid columns must be equal to number of DB columns.";
            return
        }

        Connection conn;
        if (useDB) {
            try {
                conn = mapJDBC.get("MA_TEMP_JDBC").getConnection()
            } catch (Exception e) {
                log.error("SP_WriteDataGrid2DB: Failed to create connection:" + e)
                status = "ERROR"
                errorMessage = e.getLocalizedMessage();
                return
            }
            log.info("SP_WriteDataGrid2DB - connection URL: " + conn.getMetaData().getURL());
        } else {
            log.info("SP_WriteDataGrid2DB - TEST mode, not using real DB")
        }

        // Check if there are any rows to process
        boolean hasData = false
        for (Row row : inputDatagrid.iterator()) {
            hasData = true
            break
        }

        if (!hasData) {
            log.error("SP_WriteDataGrid2DB: No data in input datagrid. Exiting")
            status = "OK"
            statusDescription = "Successful insert to DB"
            return
        }

        //Storing data into DB with streaming processing:
        String placeholders = String.join(", ", Collections.nCopies(dbColumnsList.size(), "?"))
        String sql = "INSERT INTO " + dbTable + " (" + dbColumns + ", EVENT_ID, EVENT_NAME, " + logIdColumnName + ", CREATED_DTTM) VALUES (" + placeholders + ", ?, ?, ?, SYSDATE)"

        // Log the SQL template once for debugging
        log.info("SP_WriteDataGrid2DB: SQL template: " + sql)

        PreparedStatement st = null
        try {
            if (useDB) {
                st = conn.prepareStatement(sql)
            }

            // Process rows directly without intermediate collections
            for (Row row : inputDatagrid.iterator()) {
                int index = 1;
                List<Object> values = new ArrayList<>(); // For optional detailed logging

                // Process columns in the order specified by gridColumnsList to maintain PreparedStatement parameter order
                for (int i = 0; i < gridColumnsList.size(); i++) {
                    String gridColumn = gridColumnsList.get(i);
                    String dbColumn = dbColumnsList.get(i);
                    Object value = row.columnDataGet(gridColumn)

                    log.trace("SP_WriteDataGrid2DB: Processing column. Grid: $gridColumn -> DB: $dbColumn, value: $value")
                    values.add(value);
                    setPreparedStatementValue(st, index++, value);
                }
                setPreparedStatementValue(st, index++, evtInfo.getIdentity())
                values.add(evtInfo.getIdentity())
                setPreparedStatementValue(st, index++, evtInfo.getEventName())
                values.add(evtInfo.getEventName())
                setPreparedStatementValue(st, index++, logId)
                values.add(logId)

                // Optional: Log values separately (much more memory efficient)
                if (log.isDebugEnabled()) {
                    log.debug("SP_WriteDataGrid2DB: Row values: " + values.toString());
                }

                if (useDB) {
                    st.addBatch();
                }
            }
            if (useDB) {
                st.executeBatch();
            }
        } catch (Exception e) {
            errorMessage = e.getLocalizedMessage();
            log.error("SP_WriteDataGrid2DB: oracle error:" + e)
        } finally {
            if (st != null) {
                st.close();
            }
            if (conn != null) {
                conn.close();
            }
        }

        status = "OK"
        statusDescription = "Successful insert to DB"
        log.trace("SP_WriteDataGrid2DB - End");
    }

    private static void setPreparedStatementValue(PreparedStatement statement, int index, Object value) throws SQLException {
        if (statement == null) {
            return
        }
        if (value == null) {
            statement.setNull(index, Types.NULL);
        } else if (value instanceof Long) {
            statement.setLong(index, (Long) value);
        } else if (value instanceof Double) {
            statement.setDouble(index, (Double) value);
        } else if (value instanceof String) {
            statement.setString(index, (String) value);
        } else if (value instanceof Boolean) {
            statement.setBoolean(index, (Boolean) value);
        } else if (value instanceof GregorianCalendar) {
            GregorianCalendar calendar = (GregorianCalendar) value;
            statement.setTimestamp(index, new Timestamp(calendar.getTimeInMillis()));
        } else {
            throw new SQLException("Unsupported data type: " + value.getClass().getName());
        }
    }
}